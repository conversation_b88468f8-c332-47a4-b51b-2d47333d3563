<template>
	<view class="draggable-list">
		<view 
			v-for="(item, index) in internalList" 
			:key="item.id"
			class="draggable-item"
			:class="{
				'dragging': dragState.isDragging && dragState.dragIndex === index,
				'drag-over': dragState.isDragging && dragState.targetIndex === index && dragState.targetIndex !== dragState.dragIndex
			}"
			:style="getItemStyle(index)"
			@touchstart="onTouchStart($event, index)"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd"
			@touchcancel="onTouchEnd"
			@tap="onItemTap(index)"
		>
			<text class="item-name">{{ item.name }}</text>
			<view class="item-actions">
				<view class="top-btn" @tap.stop="onMoveToTop(index)">
					<uni-icons type="arrow-up" size="20" color="#ff9500" />
				</view>
				<view class="drag-handle">
					<uni-icons type="drag" size="20" color="#ff9500" />
				</view>
				<view class="checkbox" @tap.stop="onToggleSelect(index)">
					<view class="checkbox-circle" :class="{checked: item.selected}"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';

// 定义接口
interface ListItem {
	id: string;
	name: string;
	selected: boolean;
}

// 定义props
const props = defineProps<{
	list: ListItem[];
}>();

// 定义emits
const emit = defineEmits<{
	'update:list': [list: ListItem[]];
	'item-click': [index: number];
	'move-to-top': [index: number];
}>();

// 内部列表数据
const internalList = ref<ListItem[]>([...props.list]);

// 监听外部列表变化
watch(() => props.list, (newList) => {
	internalList.value = [...newList];
}, { deep: true });

// 拖拽状态
const dragState = reactive({
	isDragging: false,
	dragIndex: -1,
	targetIndex: -1,
	startY: 0,
	currentY: 0,
	offsetY: 0,
	itemHeight: 60,
	longPressTimer: null as any,
	hasMoved: false
});

// 获取元素样式
const getItemStyle = (index: number) => {
	if (!dragState.isDragging) return {};
	
	if (index === dragState.dragIndex) {
		return {
			transform: `translateY(${dragState.offsetY}px)`,
			zIndex: '999',
			backgroundColor: 'rgba(255, 149, 0, 0.1)',
			borderRadius: '8rpx',
			boxShadow: '0 4rpx 12rpx rgba(255, 149, 0, 0.3)'
		};
	}
	
	return {};
};

// 触摸开始
const onTouchStart = (e: any, index: number) => {
	// 清除之前的定时器
	clearTimeout(dragState.longPressTimer);
	
	dragState.dragIndex = index;
	dragState.targetIndex = index;
	dragState.startY = e.touches[0].clientY;
	dragState.currentY = e.touches[0].clientY;
	dragState.offsetY = 0;
	dragState.hasMoved = false;
	dragState.isDragging = false;
	
	// 长按500ms开始拖拽
	dragState.longPressTimer = setTimeout(() => {
		if (!dragState.hasMoved) {
			dragState.isDragging = true;
			console.log('开始拖拽:', index);
		}
	}, 500);
};

// 触摸移动
const onTouchMove = (e: any) => {
	const moveDistance = Math.abs(e.touches[0].clientY - dragState.startY);
	
	// 移动超过10px取消长按
	if (moveDistance > 10) {
		dragState.hasMoved = true;
		clearTimeout(dragState.longPressTimer);
	}
	
	if (!dragState.isDragging) return;
	
	dragState.currentY = e.touches[0].clientY;
	dragState.offsetY = dragState.currentY - dragState.startY;
	
	// 计算目标位置
	const moveRows = Math.round(dragState.offsetY / dragState.itemHeight);
	let newTargetIndex = dragState.dragIndex + moveRows;
	newTargetIndex = Math.max(0, Math.min(internalList.value.length - 1, newTargetIndex));
	
	if (newTargetIndex !== dragState.targetIndex) {
		dragState.targetIndex = newTargetIndex;
	}
};

// 触摸结束
const onTouchEnd = () => {
	clearTimeout(dragState.longPressTimer);
	
	const fromIndex = dragState.dragIndex;
	const toIndex = dragState.targetIndex;
	
	// 如果是拖拽状态且位置有变化，执行排序
	if (dragState.isDragging && fromIndex !== toIndex && fromIndex >= 0 && toIndex >= 0) {
		moveItem(fromIndex, toIndex);
		uni.showToast({
			title: '排序成功',
			icon: 'success',
			duration: 1000
		});
	}
	
	// 重置状态
	resetDragState();
};

// 移动数组项
const moveItem = (fromIndex: number, toIndex: number) => {
	const newList = [...internalList.value];
	const [movedItem] = newList.splice(fromIndex, 1);
	newList.splice(toIndex, 0, movedItem);
	
	internalList.value = newList;
	emit('update:list', newList);
};

// 重置拖拽状态
const resetDragState = () => {
	clearTimeout(dragState.longPressTimer);
	dragState.isDragging = false;
	dragState.dragIndex = -1;
	dragState.targetIndex = -1;
	dragState.startY = 0;
	dragState.currentY = 0;
	dragState.offsetY = 0;
	dragState.hasMoved = false;
	dragState.longPressTimer = null;
};

// 点击事件
const onItemTap = (index: number) => {
	if (!dragState.hasMoved && !dragState.isDragging) {
		emit('item-click', index);
	}
};

// 置顶事件
const onMoveToTop = (index: number) => {
	emit('move-to-top', index);
};

// 选择切换事件
const onToggleSelect = (index: number) => {
	internalList.value[index].selected = !internalList.value[index].selected;
	emit('update:list', [...internalList.value]);
};
</script>

<style lang="scss" scoped>
.draggable-list {
	position: relative;
}

.draggable-item {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: #fff;
	transition: all 0.2s ease;
	position: relative;
	
	&.dragging {
		transition: none;
	}
	
	&.drag-over::before {
		content: '';
		position: absolute;
		top: -2rpx;
		left: 0;
		right: 0;
		height: 4rpx;
		background: #ff9500;
		border-radius: 2rpx;
	}
}

.item-name {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.item-actions {
	display: flex;
	align-items: center;
}

.top-btn, .drag-handle {
	width: 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 10rpx;
}

.checkbox {
	width: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.checkbox-circle {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	border: 2rpx solid #ccc;
	transition: all 0.2s ease;
	
	&.checked {
		background: #ff9500;
		border-color: #ff9500;
		position: relative;
		
		&::after {
			content: '';
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%) rotate(45deg);
			width: 6rpx;
			height: 12rpx;
			border: 2rpx solid #fff;
			border-left: none;
			border-top: none;
		}
	}
}
</style>
