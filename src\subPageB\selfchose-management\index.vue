<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead title="自选管理" />
		</view>

		<!-- 内容区域 -->
		<view class="content-wrapper">
			<!-- 顶部标签栏 -->
			<view class="top-tabs">
				<view class="tab-item" :class="{active: activeTab==='manage'}" @tap="switchTab('manage')">自选管理</view>
				<view class="tab-item" :class="{active: activeTab==='edit'}" @tap="switchTab('edit')">编辑表头</view>
			</view>

			<!-- 提示信息 -->
			<view class="tip-box" v-if="showTip">
				<uni-icons type="info" size="18" color="#ff9500" />
				<text class="tip-text">您的自选债券设置，将会实时同步至电脑端</text>
				<uni-icons type="closeempty" size="18" color="#999" @tap="closeTip" />
			</view>

			<!-- 滚动区域 -->
			<scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced :scroll-top="scrollTop">
				<!-- 债券列表卡片 -->
				<view class="bond-list-card">
					<view class="card-header">
						<view class="card-title">
							<image class="title-icon" :src="getAssetUrl('/home/<USER>')" mode="aspectFit" />
							<text class="title-text">债券列表</text>
						</view>
					</view>

					<!-- 表格区域 -->
					<view class="bond-table" id="bond-table">
						<!-- 表头 -->
						<view class="table-header">
							<text class="header-name">名称</text>
							<text class="header-top">置顶</text>
							<text class="header-drag">拖动</text>
							<view class="header-checkbox" @tap="toggleSelectAll">
								<view class="checkbox-circle" :class="{checked:isAllSelected}"></view>
							</view>
						</view>

						<!-- 可拖拽列表组件 -->
						<DraggableList
							:list="bondsList"
							@update:list="updateBondsList"
							@item-click="handleItemClick"
							@move-to-top="moveToTop"
						/>

						<!-- 查看更多 -->
						<view class="view-more" @tap="loadMore">
							<image class="more-icon" :src="getAssetUrl('/home/<USER>')" mode="aspectFit"></image>
							<text class="more-text">查看更多</text>
						</view>
					</view>
				</view>

				<!-- 底部留白 -->
				<view class="bottom-space"></view>
			</scroll-view>
		</view>

		<!-- 底部删除按钮 -->
		<view class="delete-btn" @tap="deleteSelected">
			<text class="btn-text">删除({{selectedCount}})</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import DraggableList from '@/components/DraggableList/DraggableList.vue';
import { ref, computed, onMounted } from 'vue';
import { getAssetUrl } from '@/config/assets';

// 当前顶部标签
const activeTab = ref<'manage' | 'edit'>('manage');
const switchTab = (tab:string) => {
	activeTab.value = tab as any;
	if(tab==='edit'){
		// TODO: 跳转或展示编辑表头功能
		uni.showToast({title:'功能开发中',icon:'none'});
	}
};

// 提示框显示
const showTip = ref(true);
const closeTip = () => showTip.value=false;

// 滚动位置
const scrollTop = ref(0);

// 债券列表
interface BondItem{
	id: string;
	name: string;
	selected: boolean;
}

const bondsList = ref<BondItem[]>([
	{id: '1', name:'中国银行股份有限公司',selected:false},
	{id: '2', name:'阿里巴巴集团控股有限公司',selected:false},
	{id: '3', name:'腾讯控股有限公司',selected:false},
	{id: '4', name:'比亚迪股份有限公司',selected:false},
	{id: '5', name:'美团点评集团',selected:false},
	{id: '6', name:'京东集团股份有限公司',selected:false},
	
]);

// 更新列表数据
const updateBondsList = (newList: any[]) => {
	bondsList.value = newList;
};

// 处理项目点击
const handleItemClick = (index: number) => {
	toggleSelect(index);
};















// 移动数组项
const moveItem = (fromIndex: number, toIndex: number) => {
	if (fromIndex === toIndex) return;
	
	const newList = [...bondsList.value];
	const [movedItem] = newList.splice(fromIndex, 1);
	newList.splice(toIndex, 0, movedItem);
	
	bondsList.value = newList;
	
	// 保存排序结果
	saveOrderChanges();
};

// 置顶功能
const moveToTop = (index: number) => {
	if (index === 0) return;
	
	moveItem(index, 0);
	
	uni.showToast({
		title: '已置顶',
		icon: 'success',
		duration: 1000
	});
};

// 保存排序变更
const saveOrderChanges = () => {
	console.log('保存排序结果', bondsList.value.map(item => item.name));
	// TODO: 实际项目中应该调用API将排序结果保存到服务器
};

const selectedCount = computed(()=>bondsList.value.filter(b=>b.selected).length);
const toggleSelect = (idx:number)=>{bondsList.value[idx].selected=!bondsList.value[idx].selected};
const deleteSelected = ()=>{
	if(selectedCount.value===0){uni.showToast({title:'请选择要删除的债券',icon:'none'});return;}
	bondsList.value=bondsList.value.filter(b=>!b.selected);
	uni.showToast({title:'删除成功',icon:'success'});
};
const loadMore=()=>uni.showToast({title:'加载更多功能开发中',icon:'none'});

// 是否全选
const isAllSelected = computed(()=>selectedCount.value>0 && selectedCount.value===bondsList.value.length);

// 切换全选/取消全选
const toggleSelectAll = () => {
	const newState = !isAllSelected.value;
	bondsList.value.forEach(b => b.selected = newState);
};

// 页面加载时获取数据
onMounted(async () => {
	// 页面初始化
	console.log('页面加载完成');
});
</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
	padding: 0 20rpx;
	height: 100vh;
	box-sizing: border-box;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

/* 1.2 头部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
	flex: 1;
	margin-top: 180rpx;
	display: flex;
	flex-direction: column;
}

/* 1.4 滚动区域 */
.scrollable-content {
	flex: 1;
}

/* 顶部标签栏 */
.top-tabs {
	display: flex;
	padding: 0 20rpx;
	margin-top: 20rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	font-size: 32rpx;
	padding: 25rpx 0;
	color: #666;
	position: relative;
}

.tab-item.active {
	font-weight: bold;
	color: #ff9500;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 40%;
	height: 6rpx;
	background-color: #ff9500;
	border-radius: 6rpx 6rpx 0 0;
}

/* 提示信息框 */
.tip-box {
	padding: 20rpx;
	border-radius: 10rpx;
	background: rgba(255, 149, 0, 0.1);
	display: flex;
	align-items: center;
    margin-bottom: 20rpx;
}

.tip-text {
	flex: 1;
	font-size: 26rpx;
	color: #ff9500;
	margin-left: 10rpx;
}

.close-icon {
	margin-left: 10rpx;
}

/* 债券列表卡片 */
.bond-list-card {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #eee;
}

.card-title {
	display: flex;
	align-items: center;
}

.title-icon {
	width: 44rpx;
	height: 44rpx;
	margin-right: 16rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 债券表格 */
.bond-table {
	padding: 0;
}

/* 表头 */
.table-header {
	display: flex;
	padding: 20rpx;
	background: linear-gradient(180deg, #fafafa 0%, #f4f4f4 100%);
	box-shadow: inset 0 -2rpx 0 #EAE9E9;
	font-size: 28rpx;
	font-weight: 500;
	color: #000;
	align-items: center;
}

.header-name {
	flex: 1;
}

.header-top, .header-drag {
	width: 100rpx;
	text-align: center;
}

.header-checkbox {
	width: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 表格内容 */
.table-body {
	padding: 0;
	position: relative;
}

.table-row {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.2s ease;
    position: relative;
    background: #fff;
    transform: translateY(0);
    z-index: 1;
    opacity: 1;
}





.bond-name {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.top-icon, .drag-icon {
	width: 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}

.drag-icon {
    touch-action: none;
    user-select: none;
    padding: 10rpx;
}

.checkbox {
	width: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.checkbox-circle {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	border: 2rpx solid #ccc;
	transition: all 0.2s ease;
}

.checkbox-circle.checked {
	background: #ff9500;
	border-color: #ff9500;
	position: relative;
}

.checkbox-circle.checked::after {
	content: '';
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%) rotate(45deg);
	width: 6rpx;
	height: 12rpx;
	border: 2rpx solid #fff;
	border-left: none;
	border-top: none;
}

/* 查看更多 */
.view-more {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30rpx 0 10rpx 0;
	font-size: 26rpx;
	color: #999999;
}

.more-icon {
	width: 30rpx;
	height: 30rpx;
	margin-bottom: 10rpx;
}

.more-text {
	margin-top: 10rpx;
}

/* 底部留白 */
.bottom-space {
	height: 120rpx; /* 增加底部留白，避免被删除按钮覆盖 */
}

/* 底部删除按钮 */
.delete-btn {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100rpx;
	border-radius: 0;
	background-image: linear-gradient(90deg, #ffba62 0%, #ff9500 100%);
	display: flex;
	justify-content: center;
	align-items: center;
}

.btn-text {
	font-size: 32rpx;
	color: #fff;
	font-weight: bold;
}
</style> 