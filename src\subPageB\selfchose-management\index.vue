<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead title="自选管理" />
		</view>

		<!-- 内容区域 -->
		<view class="content-wrapper">
			<!-- 顶部标签栏 -->
			<view class="top-tabs">
				<view class="tab-item" :class="{active: activeTab==='manage'}" @tap="switchTab('manage')">自选管理</view>
				<view class="tab-item" :class="{active: activeTab==='edit'}" @tap="switchTab('edit')">编辑表头</view>
			</view>

			<!-- 提示信息 -->
			<view class="tip-box" v-if="showTip">
				<uni-icons type="info" size="18" color="#ff9500" />
				<text class="tip-text">您的自选债券设置，将会实时同步至电脑端</text>
				<uni-icons type="closeempty" size="18" color="#999" @tap="closeTip" />
			</view>

			<!-- 滚动区域 -->
			<scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced :scroll-top="scrollTop">
				<!-- 债券列表卡片 -->
				<view class="bond-list-card">
					<view class="card-header">
						<view class="card-title">
							<image class="title-icon" :src="getAssetUrl('/home/<USER>')" mode="aspectFit" />
							<text class="title-text">债券列表</text>
						</view>
					</view>

					<!-- 表格区域 -->
					<view class="bond-table" id="bond-table">
						<!-- 表头 -->
						<view class="table-header">
							<text class="header-name">名称</text>
							<text class="header-top">置顶</text>
							<text class="header-drag">拖动</text>
							<view class="header-checkbox" @tap="toggleSelectAll">
								<view class="checkbox-circle" :class="{checked:isAllSelected}"></view>
							</view>
						</view>

						<!-- 表格内容 -->
						<view class="table-body" id="table-body">
							<view v-for="(item,index) in bondsList" :key="item.id"
								  class="table-row"
								  :id="'row-' + index"
								  :class="{
								  	'drag-over': dragState.isDragging && dragState.targetIndex === index && dragState.targetIndex !== dragState.dragIndex,
								  	'drag-placeholder': dragState.isDragging && dragState.dragId === item.id
								  }"
								  :style="getRowStyle(item.id)"
								  @touchstart="onDragStart"
								  @touchmove="onDragMove"
								  @touchend="onDragEnd"
								  @touchcancel="onDragEnd"
								  :data-index="index"
								  :data-id="item.id">
								<text class="bond-name">{{ item.name }}</text>
								<view class="top-icon" @tap.stop="moveToTop(index)">
									<uni-icons type="arrow-up" size="20" color="#ff9500" />
								</view>
								<view class="drag-icon">
									<uni-icons type="drag" size="20" color="#ff9500" />
								</view>
								<view class="checkbox" @tap.stop="toggleSelect(index)">
									<view class="checkbox-circle" :class="{checked:item.selected}"></view>
								</view>
							</view>

							<!-- 拖拽时的浮动元素 -->
							<view v-if="dragState.isDragging"
								  class="drag-ghost"
								  :style="getDragGhostStyle()">
								<text class="bond-name">{{ getDraggedItem()?.name }}</text>
								<view class="top-icon">
									<uni-icons type="arrow-up" size="20" color="#ff9500" />
								</view>
								<view class="drag-icon">
									<uni-icons type="drag" size="20" color="#ff9500" />
								</view>
								<view class="checkbox">
									<view class="checkbox-circle" :class="{checked: getDraggedItem()?.selected}"></view>
								</view>
							</view>
						</view>

						<!-- 查看更多 -->
						<view class="view-more" @tap="loadMore">
							<image class="more-icon" :src="getAssetUrl('/home/<USER>')" mode="aspectFit"></image>
							<text class="more-text">查看更多</text>
						</view>
					</view>
				</view>

				<!-- 底部留白 -->
				<view class="bottom-space"></view>
			</scroll-view>
		</view>

		<!-- 底部删除按钮 -->
		<view class="delete-btn" @tap="deleteSelected">
			<text class="btn-text">删除({{selectedCount}})</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, computed, onMounted, onUnmounted, reactive, nextTick } from 'vue';
import { getAssetUrl } from '@/config/assets';

// 当前顶部标签
const activeTab = ref<'manage' | 'edit'>('manage');
const switchTab = (tab:string) => {
	activeTab.value = tab as any;
	if(tab==='edit'){
		// TODO: 跳转或展示编辑表头功能
		uni.showToast({title:'功能开发中',icon:'none'});
	}
};

// 提示框显示
const showTip = ref(true);
const closeTip = () => showTip.value=false;

// 滚动位置
const scrollTop = ref(0);

// 债券列表
interface BondItem{
	id: string;
	name: string;
	selected: boolean;
}

const bondsList = ref<BondItem[]>([
	{id: '1', name:'中国银行股份有限公司',selected:false},
	{id: '2', name:'阿里巴巴集团控股有限公司',selected:false},
	{id: '3', name:'腾讯控股有限公司',selected:false},
	{id: '4', name:'比亚迪股份有限公司',selected:false},
	{id: '5', name:'美团点评集团',selected:false},
	{id: '6', name:'京东集团股份有限公司',selected:false},
	{id: '7', name:'小米集团',selected:false},
	{id: '8', name:'百度在线网络技术公司',selected:false},
	{id: '9', name:'网易有道信息技术公司',selected:false},
	{id: '10', name:'字节跳动科技有限公司',selected:false},
	{id: '11', name:'滴滴出行科技有限公司',selected:false},
	{id: '12', name:'拼多多集团股份有限公司',selected:false}
]);

// 拖拽状态管理
const dragState = reactive({
	isDragging: false,
	dragId: '', // 使用ID而不是索引
	dragIndex: -1,
	targetIndex: -1,
	startY: 0,
	currentY: 0,
	offsetY: 0,
	itemHeight: 0,
	tableTop: 0,
	scrollStartTop: 0,
	startTime: 0,
	hasMoved: false,
	ghostX: 0,
	ghostY: 0,
	autoScrollTimer: null as any,
	longPressTimer: null as any
});

// 获取元素位置信息
const getElementInfo = () => {
	return new Promise((resolve) => {
		const query = uni.createSelectorQuery();
		query.select('.table-row').boundingClientRect();
		query.select('#table-body').boundingClientRect();
		query.exec((res) => {
			if (res[0] && res[1]) {
				dragState.itemHeight = res[0].height;
				dragState.tableTop = res[1].top;
				resolve(res);
			}
		});
	});
};



// 拖拽开始事件 - 长按触发
const onDragStart = async (e: any) => {
	const index = parseInt(e.currentTarget.dataset.index);
	const id = e.currentTarget.dataset.id;

	// 清除之前的定时器
	clearTimeout(dragState.longPressTimer);
	clearInterval(dragState.autoScrollTimer);

	// 获取元素信息
	await getElementInfo();

	dragState.startTime = Date.now();
	dragState.hasMoved = false;
	dragState.isDragging = false;
	dragState.dragId = id;
	dragState.dragIndex = index;
	dragState.targetIndex = index;
	dragState.startY = e.touches[0].clientY;
	dragState.currentY = e.touches[0].clientY;
	dragState.offsetY = 0;
	dragState.ghostX = e.touches[0].clientX;
	dragState.ghostY = e.touches[0].clientY;
	dragState.scrollStartTop = scrollTop.value;

	// 设置长按定时器，500ms后开始拖拽
	dragState.longPressTimer = setTimeout(() => {
		if (!dragState.hasMoved) {
			dragState.isDragging = true;
			uni.vibrateShort({ type: 'light' }); // 震动反馈
			console.log('开始拖拽:', { index, id, itemHeight: dragState.itemHeight });
		}
	}, 500);

	console.log('触摸开始:', { index, id });
};

// 拖拽移动事件
const onDragMove = (e: any) => {
	const moveDistance = Math.abs(e.touches[0].clientY - dragState.startY);

	// 如果移动距离超过阈值，取消长按定时器
	if (moveDistance > 10) {
		dragState.hasMoved = true;
		clearTimeout(dragState.longPressTimer);
	}

	if (!dragState.isDragging) return;

	// 更新当前位置
	dragState.currentY = e.touches[0].clientY;
	dragState.offsetY = dragState.currentY - dragState.startY;
	dragState.ghostX = e.touches[0].clientX;
	dragState.ghostY = e.touches[0].clientY;

	// 重新计算当前拖拽元素的实际索引位置
	const currentDragIndex = bondsList.value.findIndex(item => item.id === dragState.dragId);

	// 计算目标位置
	const moveRows = Math.round(dragState.offsetY / dragState.itemHeight);
	let newTargetIndex = currentDragIndex + moveRows;
	newTargetIndex = Math.max(0, Math.min(bondsList.value.length - 1, newTargetIndex));

	if (newTargetIndex !== dragState.targetIndex) {
		dragState.targetIndex = newTargetIndex;
		console.log('拖拽目标位置:', newTargetIndex, '当前拖拽元素索引:', currentDragIndex);
	}

	// 自动滚动处理
	handleAutoScroll(e.touches[0].clientY);
};

// 拖拽结束事件
const onDragEnd = () => {
	// 清除定时器
	clearTimeout(dragState.longPressTimer);
	clearInterval(dragState.autoScrollTimer);

	// 使用ID查找当前的真实索引
	const fromIndex = bondsList.value.findIndex(item => item.id === dragState.dragId);
	const toIndex = dragState.targetIndex;

	console.log('拖拽结束:', {
		dragId: dragState.dragId,
		fromIndex,
		toIndex,
		isDragging: dragState.isDragging,
		hasMoved: dragState.hasMoved
	});

	// 如果是拖拽状态且位置有变化，执行排序
	if (dragState.isDragging && fromIndex !== toIndex && fromIndex >= 0 && toIndex >= 0) {
		// 先重置拖拽状态，避免样式混乱
		resetDragState();

		// 然后执行排序
		nextTick(() => {
			moveItem(fromIndex, toIndex);
			uni.showToast({
				title: '排序成功',
				icon: 'success',
				duration: 1000
			});
		});
	} else if (!dragState.hasMoved && !dragState.isDragging) {
		// 如果没有移动且没有拖拽，执行点击选择
		const currentIndex = bondsList.value.findIndex(item => item.id === dragState.dragId);
		if (currentIndex >= 0) {
			toggleSelect(currentIndex);
		}
		resetDragState();
	} else {
		// 重置状态
		resetDragState();
	}
};

// 改进的自动滚动处理
const handleAutoScroll = (clientY: number) => {
	const scrollThreshold = 80; // 触发自动滚动的区域高度
	const scrollSpeed = 8; // 滚动速度

	// 清除之前的自动滚动定时器
	clearInterval(dragState.autoScrollTimer);

	// 获取屏幕高度
	const systemInfo = uni.getSystemInfoSync();
	const screenHeight = systemInfo.windowHeight;

	let shouldScroll = false;
	let direction = 0;

	if (clientY < scrollThreshold) {
		// 向上滚动
		shouldScroll = true;
		direction = -1;
	} else if (clientY > screenHeight - scrollThreshold) {
		// 向下滚动
		shouldScroll = true;
		direction = 1;
	}

	if (shouldScroll) {
		dragState.autoScrollTimer = setInterval(() => {
			if (direction === -1) {
				scrollTop.value = Math.max(0, scrollTop.value - scrollSpeed);
			} else {
				scrollTop.value += scrollSpeed;
			}
		}, 16); // 约60fps
	}
};

// 重置拖拽状态
const resetDragState = () => {
	clearTimeout(dragState.longPressTimer);
	clearInterval(dragState.autoScrollTimer);

	dragState.isDragging = false;
	dragState.dragId = '';
	dragState.dragIndex = -1;
	dragState.targetIndex = -1;
	dragState.startY = 0;
	dragState.currentY = 0;
	dragState.offsetY = 0;
	dragState.hasMoved = false;
	dragState.ghostX = 0;
	dragState.ghostY = 0;
	dragState.autoScrollTimer = null;
	dragState.longPressTimer = null;
};

// 获取行样式
const getRowStyle = (id: string) => {
	if (!dragState.isDragging) return {};

	if (id === dragState.dragId) {
		// 被拖拽的元素变为透明占位符
		return {
			opacity: '0.3',
			transform: 'scale(0.95)'
		};
	}

	return {};
};

// 获取被拖拽的数据项
const getDraggedItem = () => {
	if (!dragState.dragId) return null;
	return bondsList.value.find(item => item.id === dragState.dragId);
};

// 获取拖拽浮动元素样式
const getDragGhostStyle = () => {
	if (!dragState.isDragging) return { display: 'none' };

	return {
		position: 'fixed' as const,
		left: `${dragState.ghostX - 200}px`,
		top: `${dragState.ghostY - 30}px`,
		width: '360px',
		zIndex: '9999',
		pointerEvents: 'none' as const,
		opacity: '0.8'
	};
};

// 移动数组项
const moveItem = (fromIndex: number, toIndex: number) => {
	if (fromIndex === toIndex) return;
	
	const newList = [...bondsList.value];
	const [movedItem] = newList.splice(fromIndex, 1);
	newList.splice(toIndex, 0, movedItem);
	
	bondsList.value = newList;
	
	// 保存排序结果
	saveOrderChanges();
};

// 置顶功能
const moveToTop = (index: number) => {
	if (index === 0) return;
	
	moveItem(index, 0);
	
	uni.showToast({
		title: '已置顶',
		icon: 'success',
		duration: 1000
	});
};

// 保存排序变更
const saveOrderChanges = () => {
	console.log('保存排序结果', bondsList.value.map(item => item.name));
	// TODO: 实际项目中应该调用API将排序结果保存到服务器
};

const selectedCount = computed(()=>bondsList.value.filter(b=>b.selected).length);
const toggleSelect = (idx:number)=>{bondsList.value[idx].selected=!bondsList.value[idx].selected};
const deleteSelected = ()=>{
	if(selectedCount.value===0){uni.showToast({title:'请选择要删除的债券',icon:'none'});return;}
	bondsList.value=bondsList.value.filter(b=>!b.selected);
	uni.showToast({title:'删除成功',icon:'success'});
};
const loadMore=()=>uni.showToast({title:'加载更多功能开发中',icon:'none'});

// 是否全选
const isAllSelected = computed(()=>selectedCount.value>0 && selectedCount.value===bondsList.value.length);

// 切换全选/取消全选
const toggleSelectAll = () => {
	const newState = !isAllSelected.value;
	bondsList.value.forEach(b => b.selected = newState);
};

// 页面加载时获取数据
onMounted(async () => {
	// 延迟获取元素信息，确保DOM渲染完成
	setTimeout(() => {
		getElementInfo();
	}, 500);
});

// 页面卸载时清理定时器
onUnmounted(() => {
	clearTimeout(dragState.longPressTimer);
	clearInterval(dragState.autoScrollTimer);
});
</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
	padding: 0 20rpx;
	height: 100vh;
	box-sizing: border-box;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

/* 1.2 头部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
	flex: 1;
	margin-top: 180rpx;
	display: flex;
	flex-direction: column;
}

/* 1.4 滚动区域 */
.scrollable-content {
	flex: 1;
}

/* 顶部标签栏 */
.top-tabs {
	display: flex;
	padding: 0 20rpx;
	margin-top: 20rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	font-size: 32rpx;
	padding: 25rpx 0;
	color: #666;
	position: relative;
}

.tab-item.active {
	font-weight: bold;
	color: #ff9500;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 40%;
	height: 6rpx;
	background-color: #ff9500;
	border-radius: 6rpx 6rpx 0 0;
}

/* 提示信息框 */
.tip-box {
	padding: 20rpx;
	border-radius: 10rpx;
	background: rgba(255, 149, 0, 0.1);
	display: flex;
	align-items: center;
    margin-bottom: 20rpx;
}

.tip-text {
	flex: 1;
	font-size: 26rpx;
	color: #ff9500;
	margin-left: 10rpx;
}

.close-icon {
	margin-left: 10rpx;
}

/* 债券列表卡片 */
.bond-list-card {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #eee;
}

.card-title {
	display: flex;
	align-items: center;
}

.title-icon {
	width: 44rpx;
	height: 44rpx;
	margin-right: 16rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 债券表格 */
.bond-table {
	padding: 0;
}

/* 表头 */
.table-header {
	display: flex;
	padding: 20rpx;
	background: linear-gradient(180deg, #fafafa 0%, #f4f4f4 100%);
	box-shadow: inset 0 -2rpx 0 #EAE9E9;
	font-size: 28rpx;
	font-weight: 500;
	color: #000;
	align-items: center;
}

.header-name {
	flex: 1;
}

.header-top, .header-drag {
	width: 100rpx;
	text-align: center;
}

.header-checkbox {
	width: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 表格内容 */
.table-body {
	padding: 0;
	position: relative;
}

.table-row {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.2s ease;
    position: relative;
    background: #fff;
    transform: translateY(0);
    z-index: 1;
    opacity: 1;
}

/* 拖拽相关样式 */
.table-row.dragging {
    background-color: rgba(255, 149, 0, 0.15);
    border-left: 4rpx solid #ff9500;
    box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);
    border-radius: 8rpx;
    transition: none;
}

.table-row.drag-placeholder {
    opacity: 0.3;
    transform: scale(0.95);
    transition: all 0.2s ease;
}

.table-row.drag-over::before {
    content: '';
    position: absolute;
    top: -2rpx;
    left: 0;
    right: 0;
    height: 4rpx;
    background: #ff9500;
    border-radius: 2rpx;
}

/* 拖拽浮动元素样式 */
.drag-ghost {
    display: flex;
    align-items: center;
    padding: 30rpx 20rpx;
    background: #fff;
    border-radius: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    border: 1rpx solid #ff9500;
    pointer-events: none;
    user-select: none;
}

.drag-ghost .bond-name {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.drag-ghost .top-icon,
.drag-ghost .drag-icon {
    width: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.drag-ghost .checkbox {
    width: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.bond-name {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.top-icon, .drag-icon {
	width: 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}

.drag-icon {
    touch-action: none;
    user-select: none;
    padding: 10rpx;
}

.checkbox {
	width: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.checkbox-circle {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	border: 2rpx solid #ccc;
	transition: all 0.2s ease;
}

.checkbox-circle.checked {
	background: #ff9500;
	border-color: #ff9500;
	position: relative;
}

.checkbox-circle.checked::after {
	content: '';
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%) rotate(45deg);
	width: 6rpx;
	height: 12rpx;
	border: 2rpx solid #fff;
	border-left: none;
	border-top: none;
}

/* 查看更多 */
.view-more {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30rpx 0 10rpx 0;
	font-size: 26rpx;
	color: #999999;
}

.more-icon {
	width: 30rpx;
	height: 30rpx;
	margin-bottom: 10rpx;
}

.more-text {
	margin-top: 10rpx;
}

/* 底部留白 */
.bottom-space {
	height: 120rpx; /* 增加底部留白，避免被删除按钮覆盖 */
}

/* 底部删除按钮 */
.delete-btn {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100rpx;
	border-radius: 0;
	background-image: linear-gradient(90deg, #ffba62 0%, #ff9500 100%);
	display: flex;
	justify-content: center;
	align-items: center;
}

.btn-text {
	font-size: 32rpx;
	color: #fff;
	font-weight: bold;
}
</style> 